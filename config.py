"""
Configuration management for the Advanced AI Agent.
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Union

import dotenv
from pydantic import BaseModel, Field

# Load environment variables from .env file
dotenv.load_dotenv()

# Define the available Gemini models
AVAILABLE_MODELS = {
    "gemini": [
        "gemini-1.5-flash",
        "gemini-1.5-flash-8b",
        "gemini-2.0-flash",
        "gemini-2.0-flash-001",
        "gemini-2.0-flash-lite-001",
        "gemini-2.0-flash-lite"
        "gemini-2.5-flash"
    ]
}

# Default Gemini API keys
DEFAULT_GEMINI_API_KEYS = [
    "AIzaSyCi2Y_GbSq9M2pF-Cle0ZxBuls6ciHY6tk",
    "AIzaSyBek6oPwYsAjivwEBKN6R_LXrAz1ivduUc",
    "AIzaSyCzQGWGcPAw05VsBLJpvme3HkiiddJEfYM",
    "AIzaSyCmjJlIP8qUw_lwwOv-Ids9T387q7NHqXM",
    "AIzaSyClWSACQKWlfo5Zko_JtyUEPHNbvBDBpZM",
    "AIzaSyARiGMhrYFP4ebAPhTAamHgc5TVUUkrB7M",
    "AIzaSyDRThr-yBXxdgt4hHxwkkSwgY2yxdBQ3OU",
    "AIzaSyCalOKJJ_wcrade0jz2TAoVc2kH9HsDh4g",
    "AIzaSyBKWyEdKf43AmJZVCFoJ98x5M5lQgX10u0",
    "AIzaSyAf1w6LiLEVxNSokTXU0lFmy_aj7agOosM",
    "AIzaSyAOTByGHNzEzKtJP4oWzrkwtRY_yfjTJGE"
]

class ToolConfig(BaseModel):
    """Configuration for a tool."""
    enabled: bool = True
    options: Dict[str, Any] = Field(default_factory=dict)

class AgentConfig(BaseModel):
    """Configuration for the agent."""
    model: str = "gemini-2.0-flash"
    provider: str = "gemini"
    temperature: float = 0.7
    max_tokens: int = 4096
    system_prompt: str = ""
    tools: Dict[str, ToolConfig] = Field(default_factory=dict)

class Config(BaseModel):
    """Main configuration for the Advanced AI Agent."""
    agent: AgentConfig = Field(default_factory=AgentConfig)
    api_keys: Dict[str, str] = Field(default_factory=dict)
    history_dir: Path = Field(default=Path.home() / ".advanced_ai_agent" / "history")
    log_dir: Path = Field(default=Path.home() / ".advanced_ai_agent" / "logs")
    workspace_dir: Optional[Path] = None
    debug: bool = False
    silent_logging: bool = True  # Hide logs by default
    consistent_color: str = "blue"  # Default color for responses

    class Config:
        arbitrary_types_allowed = True

def get_config_dir() -> Path:
    """Get the configuration directory."""
    config_dir = Path.home() / ".advanced_ai_agent"
    config_dir.mkdir(parents=True, exist_ok=True)
    return config_dir

def get_config_file() -> Path:
    """Get the configuration file path."""
    return get_config_dir() / "config.json"

def load_config() -> Config:
    """Load the configuration from file."""
    config_file = get_config_file()

    # Create default config if it doesn't exist
    if not config_file.exists():
        config = Config()
        save_config(config)
        return config

    try:
        with open(config_file, "r") as f:
            config_data = json.load(f)

        # Convert paths from strings to Path objects
        if "history_dir" in config_data:
            config_data["history_dir"] = Path(config_data["history_dir"])
        if "log_dir" in config_data:
            config_data["log_dir"] = Path(config_data["log_dir"])
        if "workspace_dir" in config_data and config_data["workspace_dir"]:
            config_data["workspace_dir"] = Path(config_data["workspace_dir"])

        return Config(**config_data)
    except Exception as e:
        print(f"Error loading config: {e}")
        return Config()

def save_config(config: Config) -> None:
    """Save the configuration to file."""
    config_file = get_config_file()

    # Convert Path objects to strings for JSON serialization
    config_dict = config.model_dump()
    if "history_dir" in config_dict:
        config_dict["history_dir"] = str(config_dict["history_dir"])
    if "log_dir" in config_dict:
        config_dict["log_dir"] = str(config_dict["log_dir"])
    if "workspace_dir" in config_dict and config_dict["workspace_dir"]:
        config_dict["workspace_dir"] = str(config_dict["workspace_dir"])

    with open(config_file, "w") as f:
        json.dump(config_dict, f, indent=2)

def get_api_key(provider: str) -> str:
    """Get the API key for a provider."""
    config = load_config()

    # Check if the API key is in the config
    if provider in config.api_keys:
        return config.api_keys[provider]

    # Check if the API key is in the environment
    env_key = f"{provider.upper()}_API_KEY"
    if env_key in os.environ:
        return os.environ[env_key]

    # For Gemini, use one of the default keys
    if provider == "gemini":
        return DEFAULT_GEMINI_API_KEYS[0]

    return ""

def set_api_key(provider: str, key: str) -> None:
    """Set the API key for a provider."""
    config = load_config()
    config.api_keys[provider] = key
    save_config(config)

def get_available_models() -> Dict[str, List[str]]:
    """Get the available models."""
    return AVAILABLE_MODELS
